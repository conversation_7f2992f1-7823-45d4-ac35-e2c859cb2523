# RAGFlow 与 Langfuse 提示词管理集成指南

本文档介绍如何将 Langfuse 的智能提示词推荐功能集成到 RAGFlow 等第三方系统中。

## 🎯 集成概述

通过 Langfuse 的智能提示词推荐 API，RAGFlow 用户可以：

1. **智能推荐**：根据构建意图自动推荐最合适的提示词
2. **语义搜索**：使用 AI 技术进行语义匹配，而非简单的关键词搜索
3. **快速集成**：通过简单的 REST API 调用即可集成
4. **实时同步**：获取最新的提示词版本和配置

## 🚀 API 接口

### 1. 智能推荐接口

**端点**: `GET /api/public/v2/prompts/recommend`

**认证**: Bearer Token (API Key)

**参数**:
```typescript
{
  query: string;           // 用户查询或需求描述（必需）
  intent?: string;         // 构建意图描述（可选）
  keywords?: string[];     // 关键词列表（可选）
  limit?: number;          // 返回结果数量，默认 10
  includeContent?: boolean; // 是否包含提示词内容，默认 false
  tags?: string[];         // 标签过滤（可选）
  labels?: string[];       // 标签过滤（可选）
  minScore?: number;       // 最小相似度分数，默认 0.3
}
```

**响应**:
```typescript
{
  data: Array<{
    id: string;
    name: string;
    version: number;
    type: string;
    tags: string[];
    labels: string[];
    score: number;          // 相似度分数 0-1
    matchReason: string;    // 匹配原因
    prompt?: any;           // 提示词内容（如果 includeContent=true）
    config?: any;           // 配置信息
  }>;
  meta: {
    query: string;
    totalResults: number;
    processingTimeMs: number;
    aiAnalysis?: {
      extractedKeywords: string[];
      detectedIntent: string;
      confidence: number;
    };
  };
}
```

### 2. 增强搜索接口

**端点**: `GET /api/public/v2/prompts`

**新增参数**:
```typescript
{
  search?: string;         // 搜索关键词
  searchMode?: "basic" | "semantic"; // 搜索模式
  includeContent?: boolean; // 是否包含内容
  // ... 其他现有参数
}
```

## 💻 集成示例

### JavaScript/TypeScript 集成

```typescript
class LangfusePromptRecommender {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = 'https://your-langfuse-instance.com') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  async recommendPrompts(params: {
    query: string;
    intent?: string;
    limit?: number;
    includeContent?: boolean;
  }) {
    const searchParams = new URLSearchParams({
      query: params.query,
      limit: (params.limit || 10).toString(),
      includeContent: (params.includeContent || false).toString(),
    });

    if (params.intent) {
      searchParams.append('intent', params.intent);
    }

    const response = await fetch(`${this.baseUrl}/api/public/v2/prompts/recommend?${searchParams}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`推荐失败: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }

  async searchPrompts(params: {
    search: string;
    searchMode?: 'basic' | 'semantic';
    includeContent?: boolean;
    limit?: number;
  }) {
    const searchParams = new URLSearchParams({
      search: params.search,
      searchMode: params.searchMode || 'semantic',
      includeContent: (params.includeContent || false).toString(),
      limit: (params.limit || 10).toString(),
    });

    const response = await fetch(`${this.baseUrl}/api/public/v2/prompts?${searchParams}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`搜索失败: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  }
}

// 使用示例
const recommender = new LangfusePromptRecommender('your-api-key');

// 智能推荐
const recommendations = await recommender.recommendPrompts({
  query: '我想构建一个客服聊天机器人，能够回答用户关于产品的问题',
  intent: '构建智能客服系统',
  limit: 5,
  includeContent: true,
});

console.log('推荐结果:', recommendations.data);
```

### Python 集成

```python
import requests
from typing import Optional, List, Dict, Any
from urllib.parse import urlencode

class LangfusePromptRecommender:
    def __init__(self, api_key: str, base_url: str = "https://your-langfuse-instance.com"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }

    def recommend_prompts(
        self,
        query: str,
        intent: Optional[str] = None,
        limit: int = 10,
        include_content: bool = False
    ) -> Dict[str, Any]:
        """智能推荐提示词"""
        params = {
            "query": query,
            "limit": limit,
            "includeContent": include_content
        }
        
        if intent:
            params["intent"] = intent

        url = f"{self.base_url}/api/public/v2/prompts/recommend?{urlencode(params)}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        
        return response.json()

    def search_prompts(
        self,
        search: str,
        search_mode: str = "semantic",
        include_content: bool = False,
        limit: int = 10
    ) -> Dict[str, Any]:
        """搜索提示词"""
        params = {
            "search": search,
            "searchMode": search_mode,
            "includeContent": include_content,
            "limit": limit
        }

        url = f"{self.base_url}/api/public/v2/prompts?{urlencode(params)}"
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        
        return response.json()

# 使用示例
recommender = LangfusePromptRecommender("your-api-key")

# 智能推荐
recommendations = recommender.recommend_prompts(
    query="我想构建一个客服聊天机器人，能够回答用户关于产品的问题",
    intent="构建智能客服系统",
    limit=5,
    include_content=True
)

print("推荐结果:", recommendations["data"])
```

## 🔧 RAGFlow 集成步骤

### 1. 获取 API Key

1. 登录 Langfuse 管理界面
2. 进入项目设置 → API Keys
3. 创建新的 API Key，选择适当的权限范围
4. 复制生成的 API Key

### 2. 在 RAGFlow 中添加提示词推荐按钮

```typescript
// RAGFlow 智能体配置界面组件
import React, { useState } from 'react';
import { LangfusePromptRecommender } from './langfuse-integration';

const AgentConfigPanel = () => {
  const [showPromptRecommender, setShowPromptRecommender] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState(null);
  
  const recommender = new LangfusePromptRecommender(process.env.LANGFUSE_API_KEY);

  const handlePromptRecommendation = async (agentDescription: string) => {
    try {
      const recommendations = await recommender.recommendPrompts({
        query: agentDescription,
        intent: '构建智能体',
        limit: 10,
        includeContent: true,
      });

      // 显示推荐结果
      setRecommendations(recommendations.data);
      setShowPromptRecommender(true);
    } catch (error) {
      console.error('获取推荐失败:', error);
    }
  };

  const handlePromptSelect = (prompt) => {
    setSelectedPrompt(prompt);
    // 将选中的提示词应用到智能体配置中
    applyPromptToAgent(prompt);
    setShowPromptRecommender(false);
  };

  return (
    <div className="agent-config-panel">
      {/* 现有的智能体配置界面 */}
      
      {/* 添加提示词推荐按钮 */}
      <button 
        onClick={() => handlePromptRecommendation(agentDescription)}
        className="prompt-recommend-btn"
      >
        🤖 智能推荐提示词
      </button>

      {/* 推荐结果弹窗 */}
      {showPromptRecommender && (
        <PromptRecommendationModal
          recommendations={recommendations}
          onSelect={handlePromptSelect}
          onClose={() => setShowPromptRecommender(false)}
        />
      )}
    </div>
  );
};
```

### 3. 配置环境变量

```bash
# .env 文件
LANGFUSE_API_KEY=sk-lf-your-api-key
LANGFUSE_BASE_URL=https://your-langfuse-instance.com
```

## 📊 使用场景

### 1. 智能体创建向导

当用户在 RAGFlow 中创建新的智能体时：

```typescript
// 用户输入智能体描述
const agentDescription = "创建一个能够分析财务报表的智能助手";

// 自动推荐相关提示词
const recommendations = await recommender.recommendPrompts({
  query: agentDescription,
  intent: "财务分析助手",
  tags: ["finance", "analysis"],
  limit: 5
});

// 展示推荐结果供用户选择
showRecommendations(recommendations.data);
```

### 2. 提示词优化建议

基于现有智能体的表现，推荐更好的提示词：

```typescript
// 分析当前智能体的对话历史和反馈
const currentPerformance = analyzeAgentPerformance(agentId);

// 根据性能问题推荐改进的提示词
const improvements = await recommender.recommendPrompts({
  query: `改进${currentPerformance.weaknesses.join('、')}方面的表现`,
  intent: "优化智能体性能",
  minScore: 0.5
});
```

### 3. 批量智能体配置

为多个相似的智能体快速配置提示词：

```typescript
const agentTypes = [
  { type: "customer_service", description: "客户服务智能体" },
  { type: "sales_assistant", description: "销售助手智能体" },
  { type: "technical_support", description: "技术支持智能体" }
];

for (const agentType of agentTypes) {
  const recommendations = await recommender.recommendPrompts({
    query: agentType.description,
    intent: `构建${agentType.type}`,
    limit: 3
  });
  
  // 自动应用最佳匹配的提示词
  const bestMatch = recommendations.data[0];
  if (bestMatch.score > 0.8) {
    await applyPromptToAgentType(agentType.type, bestMatch);
  }
}
```

## 🔒 安全考虑

1. **API Key 管理**：
   - 使用环境变量存储 API Key
   - 定期轮换 API Key
   - 限制 API Key 的权限范围

2. **数据隐私**：
   - 确保敏感信息不会通过 API 传输
   - 在查询中避免包含用户个人信息

3. **速率限制**：
   - 实现客户端缓存减少 API 调用
   - 处理速率限制响应

## 📈 性能优化

1. **缓存策略**：
   ```typescript
   class CachedPromptRecommender extends LangfusePromptRecommender {
     private cache = new Map();
     
     async recommendPrompts(params) {
       const cacheKey = JSON.stringify(params);
       if (this.cache.has(cacheKey)) {
         return this.cache.get(cacheKey);
       }
       
       const result = await super.recommendPrompts(params);
       this.cache.set(cacheKey, result);
       return result;
     }
   }
   ```

2. **批量请求**：
   - 合并多个相似的推荐请求
   - 使用防抖技术减少频繁调用

3. **异步处理**：
   - 在后台预加载常用推荐
   - 使用 Web Workers 处理大量数据

## 🐛 错误处理

```typescript
class RobustPromptRecommender extends LangfusePromptRecommender {
  async recommendPrompts(params, retries = 3) {
    for (let i = 0; i < retries; i++) {
      try {
        return await super.recommendPrompts(params);
      } catch (error) {
        if (i === retries - 1) throw error;
        
        // 指数退避重试
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
      }
    }
  }
}
```

## 📚 更多资源

- [Langfuse API 文档](https://langfuse.com/docs/api)
- [提示词管理最佳实践](https://langfuse.com/docs/prompts/best-practices)
- [RAGFlow 官方文档](https://ragflow.io/docs)

## 🤝 支持

如有集成问题，请：

1. 查看 [FAQ](https://langfuse.com/docs/faq)
2. 提交 [GitHub Issue](https://github.com/langfuse/langfuse/issues)
3. 加入 [Discord 社区](https://discord.gg/langfuse)
