".augment/rules/\345\274\200\345\217\221\350\256\260\345\275\225\350\247\204\345\210\231.md"
APPLICATION_PERMISSION_FIX_REPORT.md
DELETE_QUOTA_FIX_REPORT.md
DYNAMIC_HEIGHT_ADAPTATION_REPORT.md
ENHANCED_QUOTA_LIST_FINAL_REPORT.md
ENHANCED_SEARCH_FEATURE_REPORT.md
FILTERS_DEPENDENCY_FIX_REPORT.md
FINAL_SUCCESS_REPORT.md
FINAL_VERIFICATION_REPORT.md
LAYOUT_SPACE_OPTIMIZATION_REPORT.md
MULTI_QUOTA_TYPES_FEATURE_REPORT.md
OVERVIEW_STATS_IMPLEMENTATION_FINAL_REPORT.md
PAGINATION_AND_BUTTON_OPTIMIZATION_REPORT.md
PAGINATION_VISIBILITY_FIX_REPORT.md
QUOTA_DEFAULT_STATUS_FIX_REPORT.md
QUOTA_DELETE_FIX_REPORT.md
QUOTA_DIALOG_OPTIMIZATION_REPORT.md
QUOTA_LIST_DISPLAY_OPTIMIZATION_REPORT.md
QUOTA_LIST_FINAL_FIXES_REPORT.md
QUOTA_LIST_OPTIMIZATION_FINAL_REPORT.md
QUOTA_LIST_SCROLLING_FINAL_REPORT.md
QUOTA_LIST_UI_IMPROVEMENTS_REPORT.md
QUOTA_MANAGEMENT_FINAL_REPORT.md
QUOTA_MANAGEMENT_IMPROVEMENTS.md
QUOTA_MANAGEMENT_SIMPLIFIED_REPORT.md
QUOTA_STATS_SINGLE_ROW_LAYOUT_REPORT.md
QUOTA_STATS_SYNCHRONIZATION_FIX_REPORT.md
QUOTA_UNIQUENESS_CONSTRAINT_FIX_REPORT.md
QUOTA_UNIQUENESS_LOGIC_FIX_REPORT.md
SCROLLBAR_FIX_REPORT.md
SEARCH_DISPLAY_OPTIMIZATION_REPORT.md
SMART_HEIGHT_CALCULATION_REPORT.md
backups/backup-20250916-105325.bundle
backups/backup-20250916-105325.staged.diff
backups/backup-20250916-105325.untracked.txt
backups/backup-20250916-105325.working.diff
create-status-test-data.sql
create-test-tenant-data.sql
docs/third-party-integration/ragflow-integration.md
fix-tenant-stats-guide.md
packages/shared/prisma/migrations/20250907055119_add_quota_management_and_approval_workflow/migration.sql
packages/shared/prisma/migrations/20250909000000_add_lifecycle_to_quota_allocation/migration.sql
packages/shared/prisma/migrations/20250909073806_fix_quota_allocation_unique_constraint/migration.sql
packages/shared/prisma/migrations/20250909074727_change_quota_default_status_to_pending/migration.sql
web/public/sdk/langfuse-prompt-recommender.js
web/src/components/llm-test/EnhancedPromptSelector.tsx
web/src/components/llm-test/PerformanceMonitor.tsx
web/src/components/llm-test/PromptVariableEditor.tsx
web/src/components/llm-test/StreamingChat.tsx
web/src/components/ui/stats-card.tsx
web/src/features/api-management/components/ApiStats.tsx
web/src/features/api-management/components/CopyApiDialog.tsx
web/src/features/api-management/hooks/useApiApproval.ts
web/src/features/prompts/components/PromptRecommendationWidget.tsx
web/src/features/prompts/server/actions/analyzeUserIntent.ts
web/src/features/prompts/server/actions/getPromptRecommendations.ts
web/src/features/prompts/server/actions/getPromptsSearch.ts
web/src/features/prompts/server/handlers/promptRecommendHandler.ts
web/src/features/quota-management/components/CreateQuotaDialog.tsx
web/src/features/quota-management/components/EditQuotaDialog.tsx
web/src/features/quota-management/components/QuotaManagementList.tsx
web/src/features/quota-management/components/QuotaStats.tsx
web/src/features/quota-management/hooks/useQuotaManagement.ts
web/src/features/quota-management/server/quotaManagementRouter.ts
web/src/features/registration/components/ApplicationApprovalPage.tsx
web/src/features/registration/hooks/useApplicationApproval.ts
web/src/features/tenant-management/components/TenantStats.tsx
web/src/features/tenant-management/hooks/useTenantApproval.ts
web/src/pages/api/internal/llm-chat.ts
web/src/pages/api/internal/prompts-search.ts
web/src/pages/api/public/v2/prompts/recommend.ts
web/src/pages/project/[projectId]/prompts/llm-test.tsx
web/src/pages/project/[projectId]/prompts/recommend.tsx
web/src/pages/project/[projectId]/prompts/test-demo.tsx
web/src/pages/project/[projectId]/registration/quota-management.tsx
"\351\234\200\346\261\202.md"
