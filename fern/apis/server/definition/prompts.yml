# yaml-language-server: $schema=https://raw.githubusercontent.com/fern-api/fern/main/fern.schema.json
imports:
  commons: ./commons.yml
  pagination: ./utils/pagination.yml
service:
  auth: true
  base-path: /api/public/v2
  endpoints:
    get:
      docs: Get a prompt
      method: GET
      path: /prompts/{promptName}
      path-parameters:
        promptName:
          type: string
          docs: The name of the prompt
      request:
        name: GetPromptRequest
        query-parameters:
          version:
            type: optional<integer>
            docs: Version of the prompt to be retrieved.
          label:
            type: optional<string>
            docs: Label of the prompt to be retrieved. Defaults to "production" if no label or version is set.
      response: Prompt

    list:
      docs: Get a list of prompt names with versions and labels
      method: GET
      path: /prompts
      request:
        name: ListPromptsMetaRequest
        query-parameters:
          name: optional<string>
          label: optional<string>
          tag: optional<string>
          page:
            type: optional<integer>
            docs: page number, starts at 1
          limit:
            type: optional<integer>
            docs: limit of items per page
          fromUpdatedAt:
            type: optional<datetime>
            docs: Optional filter to only include prompt versions created/updated on or after a certain datetime (ISO 8601)
          toUpdatedAt:
            type: optional<datetime>
            docs: Optional filter to only include prompt versions created/updated before a certain datetime (ISO 8601)
          search:
            type: optional<string>
            docs: Optional search query across name, tags, labels, config and prompt content
          searchMode:
            type: optional<string>
            docs: Search mode: "basic" or "semantic"
          includeContent:
            type: optional<boolean>
            docs: If true, include prompt content in the response

      response: PromptMetaListResponse

    recommend:
      docs: Recommend prompts for a given query, with optional keywords/intent. Matches tags, labels and prompt content.
      method: GET
      path: /prompts/recommend
      request:
        name: RecommendPromptsRequest
        query-parameters:
          query:
            type: string
            docs: User query or intent description
          keywords: optional<list<string>>
          intent: optional<string>
          limit:
            type: optional<integer>
            docs: Number of results to return (1-20). Defaults to 10.
          includeContent:
            type: optional<boolean>
            docs: Whether to include prompt content in the response
          tags: optional<list<string>>
          labels: optional<list<string>>
          minScore:
            type: optional<double>
            docs: Minimum similarity score (0-1). Defaults to 0.3
      response: RecommendPromptsResponse

    invoke:
      docs: Invoke a prompt (test run) by name and optional version/label. Non-stream JSON response by default. If `stream=true`, returns Server-Sent Events (SSE).
      method: POST
      path: /prompts/invoke
      request:
        name: PromptInvokeRequest
        body: PromptInvokeRequest
      response: PromptInvokeResponse

types:
  PromptMetaListResponse:
    properties:
      data: list<PromptMeta>
      meta: pagination.MetaResponse

  PromptMeta:
    properties:
      name: string
      versions: list<integer>
      labels: list<string>
      tags: list<string>
      lastUpdatedAt: datetime
      lastConfig:
        type: unknown
        docs: Config object of the most recent prompt version that matches the filters (if any are provided)

  RecommendPromptsResponse:
    properties:
      data: list<PromptRecommendation>
      meta:
        properties:
          query: string
          totalResults: integer
          processingTimeMs: integer
          aiAnalysis: optional<PromptAIAnalysis>

  PromptRecommendation:
    properties:
      id: string
      name: string
      version: integer
      type: string
      tags: list<string>
      labels: list<string>
      config: unknown
      createdAt: datetime
      updatedAt: datetime
      score: double
      matchReason: string
      prompt: optional<unknown>

  PromptAIAnalysis:
    properties:
      extractedKeywords: list<string>
      detectedIntent: string
      confidence: double

  Prompt:
    union:
      chat: ChatPrompt
      text: TextPrompt

  BasePrompt:
    properties:
      name: string
      version: integer
      config: unknown
      labels:
        type: list<string>
        docs: List of deployment labels of this prompt version.
      tags:
        type: list<string>
        docs: List of tags. Used to filter via UI and API. The same across versions of a prompt.
      commitMessage:
        type: optional<string>
        docs: Commit message for this prompt version.
      resolutionGraph:
        type: optional<map<string, unknown>>
        docs: The dependency resolution graph for the current prompt. Null if prompt has no dependencies.

  ChatMessageWithPlaceholders:
    union:
      chatmessage: ChatMessage
      placeholder: PlaceholderMessage

  ChatMessage:
    properties:
      role:
        type: string
      content:
        type: string

  PlaceholderMessage:
    properties:
      name:
        type: string

  TextPrompt:
    extends: BasePrompt
    properties:
      prompt: string

  ChatPrompt:
    extends: BasePrompt
    properties:
      prompt: list<ChatMessageWithPlaceholders>

  PromptInvokeRequest:
    properties:
      promptName: string
      version: optional<integer>
      label: optional<string>
      variables: optional<map<string, string>>
      messages: optional<list<ChatMessage>>
      modelConfig: ModelConfig
      stream: optional<boolean>

  ModelConfig:
    properties:
      baseUrl: string
      modelName: string
      authKey: string
      temperature: double
      maxTokens: integer
      topP: double

  PromptInvokeResponse:
    docs: The upstream model provider response plus a metadata field when non-streaming. If stream=true, the endpoint returns SSE stream and this schema does not apply.
    properties:
      metadata:
        properties:
          promptName: string
          version: optional<integer>
          label: optional<string>
          model: string
          timestamp: datetime
          usage: unknown
