{"name": "@langfuse/ee", "version": "1.0.0", "private": true, "main": "./dist/src/index.js", "types": "./dist/src/index.d.ts", "exports": {".": {"import": "./dist/src/index.js", "require": "./dist/src/index.js"}, "./sso": {"import": "./dist/src/sso/index.js", "require": "./dist/src/sso/index.js"}}, "engines": {"node": "24"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 0", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"@langfuse/shared": "workspace:*", "@opentelemetry/api": ">=1.0.0 <1.10.0", "https-proxy-agent": "^7.0.6", "next": "^14.2.30", "next-auth": "^4.24.11", "zod": "^3.25.62"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^24.3.0", "@typescript-eslint/parser": "^7.12.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.6.2", "ts-node": "^10.9.2", "tsc-watch": "^6.2.0", "typescript": "^5.7.2"}}