---
type: "agent_requested"
description: "Example description"
---

## 开发记录 – [日期]

### 记录信息
- **开发者**: [Claude Code / Trae AI]
- **日期**: YYYY-MM-DD
- **时间**: HH:MM – HH:MM
- **会话ID**: [可选，用于追踪具体会话]

### 开发目标
简要描述本次开发要解决的问题或实现的功能:
- [ ] 主要目标1
- [ ] 主要目标2
- [ ] 主要目标3

### 项目状态检查（开发前）
- **TypeScript编译状态**: ✅ 无错误 / ❌ 有错误
- **服务运行状态**: 前端 ✅/❌ | 后端 ✅/❌
- **已知问题**: [列出检查时发现的问题]
- **Git状态**: [当前分支，未提交的更改等]

### 技术选择和架构决策
记录本次开发中的重要技术选择:

### 使用的技术栈
- **前端**: [React, TypeScript, Ant Design等]
- **后端**: [Express, Prisma，等]
- **新增依赖**: [如果有新增的npm包]
- **AI服务**: [使用的AI模型和服务]

### 架构决策
- **设计模式**: [使用的设计模式]
- **数据存储**: [数据库选择和结构变更]
- **API设计**: [新增或修改的API接口]

### 文件变更清单
记录所有创建、修改、删除的文件:

**新增文件**:
- `path/to/new-file.ts` - [文件作用说明]
- `path/to/another-file.tsx` - [文件作用说明]

**修改文件**:
- `path/to/existing-file.ts` - [修改内容说明]
- `path/to/another-existing-file.tsx` - [修改内容说明]

**删除文件**:
- `path/to/deleted-file.ts` - [删除原因]

### 数据库变更
- **Schema变更**: [Prisma模型的修改]
- **迁移文件**: [新增的迁移文件名]
- **数据种子**: [测试数据的变更]

### API变更
记录新增或修改的API接口:

**新增接口**:
- `POST /api/new-endpoint` - [接口作用]
- `GET /api/another-endpoint` - [接口作用]

**修改接口**:
- `PUT /api/existing-endpoint` - [修改内容]

**废弃接口**:
- `DELETE /api/old-endpoint` - [废弃原因]

### 前端变更（如适用）
- **新增组件**: [列出新增的React组件]
- **页面修改**: [修改的页面和路由]
- **样式变更**: [CSS/样式的重要修改]
- **状态管理**: [Redux/Zustand状态的变更]

### 后端变更（如适用）
- **控制器**: [新增或修改的控制器]
- **服务层**: [业务逻辑变更]
- **中间件**: [认证、权限等中间件变更]
```