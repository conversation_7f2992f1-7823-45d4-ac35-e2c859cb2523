name: Snyk Container
on:
  push:
    branches: ["main"]

jobs:
  snyk:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Scan web image with Snyk
        uses: snyk/actions/docker@master
        continue-on-error: true # let upload step always run
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: langfuse/langfuse # pulled from Docker Hub
          args: --severity-threshold=high # (any extra CLI flags, but NO --file)

      - name: Scan worker image with Snyk
        uses: snyk/actions/docker@master
        continue-on-error: true # let upload step always run
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          image: langfuse/langfuse-worker # pulled from Docker Hub
          args: --severity-threshold=high # (any extra CLI flags, but NO --file)
